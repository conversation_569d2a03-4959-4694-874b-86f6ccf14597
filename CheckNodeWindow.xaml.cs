using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using vocabulary.Models;
using vocabulary.Services;

namespace vocabulary
{
    /// <summary>
    /// CheckNodeWindow.xaml 的交互逻辑
    /// </summary>
    public partial class CheckNodeWindow : Window
    {
        private readonly CheckNodeService _checkNodeService;
        private string _checkSerialNum;

        public CheckNodeWindow()
        {
            InitializeComponent();
            _checkNodeService = new CheckNodeService();

            // 添加窗口拖拽功能
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();

            // 解析命令行参数
            ParseCommandLineArguments();

            // 加载检查节点信息
            LoadCheckNodeInfoAsync();
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        public CheckNodeWindow(string checkSerialNum)
        {
            InitializeComponent();
            _checkNodeService = new CheckNodeService();
            _checkSerialNum = checkSerialNum;

            // 添加窗口拖拽功能
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();

            // 加载检查节点信息
            LoadCheckNodeInfoAsync();
        }

        /// <summary>
        /// 解析命令行参数
        /// </summary>
        private void ParseCommandLineArguments()
        {
            try
            {
                string[] args = Environment.GetCommandLineArgs();
                if (args.Length > 1)
                {
                    string arg = args[1];
                    if (arg.StartsWith("checknode#", StringComparison.OrdinalIgnoreCase))
                    {
                        _checkSerialNum = arg.Substring(10);
                        System.Diagnostics.Debug.WriteLine($"解析到检查流水号：{_checkSerialNum}");
                    }
                }

                if (string.IsNullOrEmpty(_checkSerialNum))
                {
                    // 如果没有传入参数，使用测试数据
                    _checkSerialNum = "TEST001";
                    System.Diagnostics.Debug.WriteLine("未找到检查流水号参数，使用测试数据");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析命令行参数失败：{ex.Message}");
                _checkSerialNum = "TEST001";
            }
        }

        /// <summary>
        /// 异步加载检查节点信息
        /// </summary>
        private async void LoadCheckNodeInfoAsync()
        {
            try
            {
                // 显示加载状态
                txtPatientName.Text = "正在加载...";

                // 从数据库获取检查节点信息
                CheckNodeInfo checkNodeInfo = await _checkNodeService.GetCheckNodeInfoAsync(_checkSerialNum);

                if (checkNodeInfo == null)
                {
                    MessageBox.Show($"未找到对应的检查节点信息！\n\n检查流水号：{_checkSerialNum}",
                        "数据错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    this.Close();
                    return;
                }

                // 更新患者信息显示
                UpdatePatientInfoDisplay(checkNodeInfo);

                // 创建并显示流程节点
                CreateTimelineNodes(checkNodeInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载检查节点信息失败：{ex.Message}", "数据库错误", MessageBoxButton.OK, MessageBoxImage.Error);
                this.Close();
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 更新患者信息显示
        /// </summary>
        /// <param name="checkNodeInfo">检查节点信息</param>
        private void UpdatePatientInfoDisplay(CheckNodeInfo checkNodeInfo)
        {
            txtPatientName.Text = checkNodeInfo.PatientName ?? "未知";
            txtSex.Text = checkNodeInfo.Sex ?? "未知";
            txtAge.Text = checkNodeInfo.Age ?? "未知";
            txtPatientType.Text = checkNodeInfo.PatientType ?? "未知";
            txtStudyScription.Text = checkNodeInfo.StudyScription ?? "未知";
        }

        /// <summary>
        /// 创建时间线节点
        /// </summary>
        /// <param name="checkNodeInfo">检查节点信息</param>
        private void CreateTimelineNodes(CheckNodeInfo checkNodeInfo)
        {
            // 获取显示项列表
            CheckNodeDisplayResult displayResult = _checkNodeService.ConvertToDisplayItems(checkNodeInfo);

            // 清空现有节点
            FirstRowNodes.Children.Clear();
            SecondRowNodes.Children.Clear();

            // 创建第一行和第二行节点
            CreateNodeRow(FirstRowNodes, displayResult.FirstRow);
            CreateNodeRow(SecondRowNodes, displayResult.SecondRow);

            // 调整垂直连接线位置，确保对齐第一行最后一个节点
            AdjustVerticalConnectorPosition(displayResult.FirstRow.Count, displayResult.SecondRow.Count);
        }

        /// <summary>
        /// 调整垂直连接线位置
        /// </summary>
        /// <param name="firstRowCount">第一行节点数量</param>
        /// <param name="secondRowCount">第二行节点数量</param>
        private void AdjustVerticalConnectorPosition(int firstRowCount, int secondRowCount)
        {
            if (firstRowCount == 0 || secondRowCount == 0)
            {
                VerticalConnector.Visibility = Visibility.Collapsed;
                return;
            }

            VerticalConnector.Visibility = Visibility.Visible;

            // 每个节点宽度120px，连接线宽度80px
            double nodeWidth = 120;
            double connectorWidth = 80;

            // 计算第一行的总宽度
            double firstRowTotalWidth = firstRowCount * nodeWidth + (firstRowCount - 1) * connectorWidth;

            // 计算第二行的总宽度
            double secondRowTotalWidth = secondRowCount * nodeWidth + (secondRowCount - 1) * connectorWidth;

            // 第一行最后一个节点的中心位置（从第一行容器右边算起）
            double firstRowLastNodeCenter = nodeWidth / 2;

            // 第二行最后一个节点的中心位置（从第二行容器右边算起）
            double secondRowLastNodeCenter = nodeWidth / 2;

            // 计算第二行需要的右边距，使其最后一个节点与第一行最后一个节点对齐
            // 第一行右边距是60，所以第一行最后节点中心距离窗口右边的距离是 60 + nodeWidth/2
            double firstRowLastNodeFromWindowRight = 60 + firstRowLastNodeCenter;

            // 第二行需要的右边距 = 第一行最后节点距窗口右边距离 - 第二行最后节点中心位置
            double secondRowRightMargin = firstRowLastNodeFromWindowRight - secondRowLastNodeCenter;

            // 调整第二行的右边距
            SecondRowNodes.Margin = new Thickness(0, 0, secondRowRightMargin, 0);

            // 垂直连接线位置：从第一行最后一个节点中心向下
            VerticalConnector.Margin = new Thickness(0, 20, firstRowLastNodeFromWindowRight - 1, 20);
        }

        /// <summary>
        /// 创建节点行
        /// </summary>
        /// <param name="container">容器</param>
        /// <param name="items">显示项列表</param>
        private void CreateNodeRow(StackPanel container, List<CheckNodeDisplayItem> items)
        {
            for (int i = 0; i < items.Count; i++)
            {
                var item = items[i];

                // 创建节点容器
                StackPanel nodeContainer = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Width = 120
                };

                // 创建圆形节点
                Ellipse circle = new Ellipse
                {
                    Width = 48,
                    Height = 48,
                    Fill = item.IsCompleted ? new SolidColorBrush(Color.FromRgb(22, 93, 255)) : new SolidColorBrush(Color.FromRgb(148, 163, 184))
                };

                // 创建图标（使用文本代替FontAwesome图标）
                TextBlock icon = new TextBlock
                {
                    Text = item.IsCompleted ? "✓" : "○",
                    FontSize = 20,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                // 创建包含圆形和图标的Grid
                Grid circleGrid = new Grid();
                circleGrid.Children.Add(circle);
                circleGrid.Children.Add(icon);

                // 创建节点标签
                TextBlock label = new TextBlock
                {
                    Text = item.NodeName,
                    FontSize = 12,
                    FontWeight = FontWeights.Medium,
                    Foreground = new SolidColorBrush(Color.FromRgb(30, 41, 59)),
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 8, 0, 4)
                };

                // 创建操作人信息
                TextBlock operatorInfo = new TextBlock
                {
                    Text = item.OperatorName,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(100, 116, 139)),
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 2, 0, 0)
                };

                // 创建时间信息
                TextBlock timeInfo = new TextBlock
                {
                    Text = item.OperateTime,
                    FontSize = 12,
                    Foreground = new SolidColorBrush(Color.FromRgb(100, 116, 139)),
                    TextAlignment = TextAlignment.Center,
                    Margin = new Thickness(0, 2, 0, 0)
                };

                // 添加到节点容器
                nodeContainer.Children.Add(circleGrid);
                nodeContainer.Children.Add(label);
                nodeContainer.Children.Add(operatorInfo);
                nodeContainer.Children.Add(timeInfo);

                // 添加到行容器
                container.Children.Add(nodeContainer);

                // 添加连接线（除了最后一个节点）
                if (i < items.Count - 1)
                {
                    Rectangle connector = new Rectangle
                    {
                        Height = 2,
                        Width = 80,
                        Fill = new SolidColorBrush(Color.FromRgb(148, 163, 184)),
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(0, 0, 0, 60) // 调整位置使其与圆形对齐
                    };
                    container.Children.Add(connector);
                }
            }
        }
    }
}
