﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace vocabulary
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            // 统一处理所有命令行参数
            if (e.Args.Length > 0)
            {
                string arg = e.Args[0];

                // 处理患者信息核对参数：verifyinfo#userid#checkSerialNum
                if (arg.StartsWith("verifyinfo#", StringComparison.OrdinalIgnoreCase))
                {
                    string[] parts = arg.Split('#');
                    if (parts.Length == 3)
                    {
                        PatientCheckWindow patientCheckWindow = new PatientCheckWindow();
                        patientCheckWindow.Show();
                        return;
                    }
                }

                // 处理操作日志参数：operatelog#checkserialnum
                else if (arg.StartsWith("operatelog#", StringComparison.OrdinalIgnoreCase))
                {
                    string checkSerialNum = arg.Substring(11);
                    if (!string.IsNullOrEmpty(checkSerialNum))
                    {
                        OperateLogWindow logWindow = new OperateLogWindow(checkSerialNum);
                        logWindow.Show();
                        return;
                    }
                }

                // 处理检查节点参数：checknode#checkserialnum
                else if (arg.StartsWith("checknode#", StringComparison.OrdinalIgnoreCase))
                {
                    string checkSerialNum = arg.Substring(10);
                    if (!string.IsNullOrEmpty(checkSerialNum))
                    {
                        CheckNodeWindow checkNodeWindow = new CheckNodeWindow(checkSerialNum);
                        checkNodeWindow.Show();
                        return;
                    }
                }

                // 处理系统登录参数：syslogin#userid
                else if (arg.StartsWith("syslogin#", StringComparison.OrdinalIgnoreCase))
                {
                    string userId = arg.Substring(9);
                    if (!string.IsNullOrEmpty(userId))
                    {
                        LoginWindow loginWindow = new LoginWindow(userId);
                        loginWindow.Show();
                        return;
                    }
                }
            }

            // 默认启动登录窗口（无参数或参数格式错误）
            LoginWindow defaultLoginWindow = new LoginWindow();
            defaultLoginWindow.Show();
        }
    }
}
