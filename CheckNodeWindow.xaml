<Window x:Class="vocabulary.CheckNodeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="检查流程节点信息" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">
    
    <Window.Resources>
        <!-- 样式定义 -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.1" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="TitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#1E293B"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#64748B"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
        </Style>

        <Style x:Key="ValueStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#1E293B"/>
        </Style>

        <Style x:Key="NodeCircleStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="48"/>
            <Setter Property="Height" Value="48"/>
            <Setter Property="Stroke" Value="Transparent"/>
            <Setter Property="StrokeThickness" Value="2"/>
        </Style>

        <Style x:Key="CompletedNodeStyle" TargetType="Ellipse" BasedOn="{StaticResource NodeCircleStyle}">
            <Setter Property="Fill" Value="#165DFF"/>
        </Style>

        <Style x:Key="PendingNodeStyle" TargetType="Ellipse" BasedOn="{StaticResource NodeCircleStyle}">
            <Setter Property="Fill" Value="#94A3B8"/>
        </Style>

        <Style x:Key="NodeLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#1E293B"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
        </Style>

        <Style x:Key="NodeMetaStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Foreground" Value="#64748B"/>
            <Setter Property="TextAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
        </Style>

        <Style x:Key="ConnectorLineStyle" TargetType="Rectangle">
            <Setter Property="Height" Value="2"/>
            <Setter Property="Fill" Value="#94A3B8"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Width" Value="80"/>
        </Style>
    </Window.Resources>

    <!-- 主容器，添加圆角和阴影 -->
    <Border Background="#F8FAFC" CornerRadius="12" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="15"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 自定义标题栏 -->
            <Border Grid.Row="0" Background="#165DFF" CornerRadius="12,12,0,0" Height="50">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 标题文本 -->
                    <TextBlock Grid.Column="0" Text="检查流程节点信息"
                               FontSize="18" FontWeight="Bold" Foreground="White"
                               VerticalAlignment="Center" HorizontalAlignment="Left"
                               Margin="20,0,0,0"/>

                    <!-- 关闭按钮 -->
                    <Button Grid.Column="1" x:Name="CloseButton" Content="✕"
                            Width="45" Height="30" Margin="0,10,15,10"
                            Background="Transparent" Foreground="White"
                            BorderThickness="0" FontSize="16" FontWeight="Bold"
                            Cursor="Hand" Click="CloseButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="4">
                                                <ContentPresenter HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#FF4444"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>
            </Border>

            <!-- 患者信息卡片 -->
            <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="20,10,20,0">
                <StackPanel>
                    <TextBlock Text="患者信息" Style="{StaticResource TitleStyle}"/>

                    <!-- 第一行：姓名、性别、年龄、检查类型 -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                        <StackPanel Orientation="Horizontal" Margin="0,0,60,0">
                            <TextBlock Text="姓名:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="txtPatientName" Text="-" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,60,0">
                            <TextBlock Text="性别:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="txtSex" Text="-" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,60,0">
                            <TextBlock Text="年龄:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="txtAge" Text="-" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="检查类型:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock x:Name="txtPatientType" Text="-" Style="{StaticResource ValueStyle}"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 第二行：检查部位 -->
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="检查部位:" Style="{StaticResource LabelStyle}"/>
                        <TextBlock x:Name="txtStudyScription" Text="-" Style="{StaticResource ValueStyle}"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- 流程节点时间线 -->
            <Border Grid.Row="2" Style="{StaticResource CardStyle}" Margin="20,0,20,20">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <StackPanel x:Name="TimelineContainer" Margin="20">
                        <!-- 第一行：5个节点 -->
                        <StackPanel x:Name="FirstRowNodes" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,90,0">
                            <!-- 节点将通过代码动态添加 -->
                        </StackPanel>

                        <!-- 垂直连接线 -->
                        <Rectangle x:Name="VerticalConnector" Width="2" Height="32" Fill="#94A3B8"
                                   HorizontalAlignment="Right" Margin="0,20,60,20"/>

                        <!-- 第二行：3个节点 -->
                        <StackPanel x:Name="SecondRowNodes" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,240,0">
                            <!-- 节点将通过代码动态添加 -->
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Border>
</Window>
