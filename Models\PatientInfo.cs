using System;
using System.Collections.Generic;

namespace vocabulary.Models
{
    /// <summary>
    /// 检查节点信息模型
    /// </summary>
    public class CheckNodeInfo
    {
        /// <summary>
        /// 设备类型ID
        /// </summary>
        public string DeviceTypeId { get; set; }

        /// <summary>
        /// 患者类型
        /// </summary>
        public string PatientType { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public string Age { get; set; }

        /// <summary>
        /// 检查部位
        /// </summary>
        public string StudyScription { get; set; }

        /// <summary>
        /// 预约/分诊时间
        /// </summary>
        public DateTime? SeperateTime { get; set; }

        /// <summary>
        /// 分诊操作人
        /// </summary>
        public string SeperateId { get; set; }

        /// <summary>
        /// 入室核对时间
        /// </summary>
        public DateTime? CheckVerifyTime { get; set; }

        /// <summary>
        /// 入室核对人
        /// </summary>
        public string CheckVerifyId { get; set; }

        /// <summary>
        /// 检查开始时间
        /// </summary>
        public DateTime? CheckStartTime { get; set; }

        /// <summary>
        /// 检查开始人
        /// </summary>
        public string CheckStartId { get; set; }

        /// <summary>
        /// 检查完成时间
        /// </summary>
        public DateTime? StudyTime { get; set; }

        /// <summary>
        /// 检查完成人
        /// </summary>
        public string PhotoerId { get; set; }

        /// <summary>
        /// 危急值上报时间
        /// </summary>
        public DateTime? CriticalTime { get; set; }

        /// <summary>
        /// 危急值上报人
        /// </summary>
        public string CriticalerId { get; set; }

        /// <summary>
        /// 报告时间
        /// </summary>
        public DateTime? RptCommitTime { get; set; }

        /// <summary>
        /// 报告医生
        /// </summary>
        public string RptCommitDoc { get; set; }

        /// <summary>
        /// 审核时间
        /// </summary>
        public DateTime? RptEndTime { get; set; }

        /// <summary>
        /// 审核医生
        /// </summary>
        public string RptEndDoc { get; set; }
    }

    /// <summary>
    /// 检查节点显示项模型（用于界面显示）
    /// </summary>
    public class CheckNodeDisplayItem
    {
        /// <summary>
        /// 节点名称
        /// </summary>
        public string NodeName { get; set; }

        /// <summary>
        /// 操作人
        /// </summary>
        public string OperatorName { get; set; }

        /// <summary>
        /// 操作时间
        /// </summary>
        public string OperateTime { get; set; }

        /// <summary>
        /// 是否已完成
        /// </summary>
        public bool IsCompleted { get; set; }

        /// <summary>
        /// 是否为当前节点
        /// </summary>
        public bool IsCurrent { get; set; }
    }

    /// <summary>
    /// 检查节点显示结果（用于.NET 4.5.2兼容性）
    /// </summary>
    public class CheckNodeDisplayResult
    {
        /// <summary>
        /// 第一行节点
        /// </summary>
        public List<CheckNodeDisplayItem> FirstRow { get; set; }

        /// <summary>
        /// 第二行节点
        /// </summary>
        public List<CheckNodeDisplayItem> SecondRow { get; set; }

        public CheckNodeDisplayResult()
        {
            FirstRow = new List<CheckNodeDisplayItem>();
            SecondRow = new List<CheckNodeDisplayItem>();
        }
    }

    /// <summary>
    /// 节点排序项（用于.NET 4.5.2兼容性，替代元组）
    /// </summary>
    public class NodeSortItem
    {
        /// <summary>
        /// 节点显示项
        /// </summary>
        public CheckNodeDisplayItem Node { get; set; }

        /// <summary>
        /// 节点时间
        /// </summary>
        public DateTime? Time { get; set; }

        /// <summary>
        /// 节点类型
        /// </summary>
        public string Type { get; set; }

        public NodeSortItem(CheckNodeDisplayItem node, DateTime? time, string type)
        {
            Node = node;
            Time = time;
            Type = type;
        }
    }


    public class PatientInfo
    {
        public string PatientName { get; set; }
        public string InfoType { get; set; }
        public string Sex { get; set; }
        public string StudyId { get; set; }
        public string StudyScription { get; set; }
        public string DeviceName { get; set; }
    }

    public class OperateLogInfo
    {
        public string 操作医生 { get; set; }
        public string 事件名称 { get; set; }
        public string 操作时间 { get; set; }
    }

    /// <summary>
    /// 患者检查信息模型
    /// </summary>
    public class PatientCheckInfo
    {
        /// <summary>
        /// 患者信息类型（门诊CT、门诊DR等）
        /// </summary>
        public string TypeName { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别和年龄（合并字段）
        /// </summary>
        public string SexAndAge { get; set; }

        /// <summary>
        /// 性别（用于队列验证）
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 患者编号
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// 检查号
        /// </summary>
        public string StudyId { get; set; }

        /// <summary>
        /// 科室和床号（合并字段）
        /// </summary>
        public string DeptAndBed { get; set; }

        /// <summary>
        /// 检查项目
        /// </summary>
        public string StudyScription { get; set; }

        /// <summary>
        /// 入室核对人
        /// </summary>
        public string CheckVerifyId { get; set; }

        /// <summary>
        /// 入室核对时间
        /// </summary>
        public DateTime? CheckVerifyTime { get; set; }

        /// <summary>
        /// 排队号
        /// </summary>
        public string QueueNo { get; set; }

        /// <summary>
        /// 当前登录用户
        /// </summary>
        public string CurrentUser { get; set; }
    }

    /// <summary>
    /// 队列信息模型
    /// </summary>
    public class QueueInfo
    {
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 性别
        /// </summary>
        public string Sex { get; set; }

        /// <summary>
        /// 检查号
        /// </summary>
        public string StudyId { get; set; }
    }
}