using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Xml;
using vocabulary.Models;

namespace vocabulary.Services
{
    /// <summary>
    /// 检查节点信息服务
    /// </summary>
    public class CheckNodeService
    {
        private readonly DatabaseService _databaseService;

        public CheckNodeService()
        {
            _databaseService = new DatabaseService();
        }

        /// <summary>
        /// 获取检查节点信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <returns>检查节点信息</returns>
        public async Task<CheckNodeInfo> GetCheckNodeInfoAsync(string checkSerialNum)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始查询检查节点信息，检查号：{checkSerialNum}");

                string sql = $@"
                    select t.devicetypeid,
                           t.patientname,
                           t.sex,
                           t.age,
                           t.studyscription,
                           t.seperatetime,
                           t.seperateID,
                           t.checkverifytime,
                           t.checkverifyid,
                           t.checkstarttime,
                           t.checkstartid,
                           t.studytime,
                           t.photoerid,
                           t.criticaltime,
                           t.criticalerID,
                           t.rptcommittime,
                           t.rptcommitdoc,
                           t.rptendtime,
                           t.rptenddoc
                      from view_pacs_checkbnode t 
                     where t.checkserialnum = '{checkSerialNum}'";

                System.Diagnostics.Debug.WriteLine($"检查节点查询SQL：{sql}");

                string result = await _databaseService.ExecuteQueryAsync(sql);
                System.Diagnostics.Debug.WriteLine($"检查节点查询结果：{result}");

                return ParseCheckNodeInfoFromXml(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查询检查节点信息失败：{ex.Message}");
                throw new Exception($"查询检查节点信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 从XML结果解析检查节点信息
        /// </summary>
        /// <param name="xmlResult">XML查询结果</param>
        /// <returns>检查节点信息</returns>
        private CheckNodeInfo ParseCheckNodeInfoFromXml(string xmlResult)
        {
            try
            {
                if (string.IsNullOrEmpty(xmlResult))
                {
                    System.Diagnostics.Debug.WriteLine("XML结果为空");
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);

                // 创建命名空间管理器
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                // 查找rs:data节点
                XmlNode dataNode = doc.SelectSingleNode("//rs:data", nsmgr);
                if (dataNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到rs:data节点");
                    return null;
                }

                // 查找z:row节点
                XmlNodeList rowNodes = dataNode.SelectNodes("z:row", nsmgr);
                if (rowNodes == null || rowNodes.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("未找到z:row数据行");
                    return null;
                }

                XmlNode firstRow = rowNodes[0];
                CheckNodeInfo checkNodeInfo = new CheckNodeInfo();

                // 解析各个字段（从属性中获取值）
                checkNodeInfo.DeviceTypeId = GetXmlAttributeValue(firstRow, "DEVICETYPEID");
                checkNodeInfo.PatientName = GetXmlAttributeValue(firstRow, "PATIENTNAME");
                checkNodeInfo.Sex = GetXmlAttributeValue(firstRow, "SEX");
                checkNodeInfo.Age = GetXmlAttributeValue(firstRow, "AGE");
                checkNodeInfo.StudyScription = GetXmlAttributeValue(firstRow, "STUDYSCRIPTION");
                checkNodeInfo.SeperateId = GetXmlAttributeValue(firstRow, "SEPERATEID");
                checkNodeInfo.CheckVerifyId = GetXmlAttributeValue(firstRow, "CHECKVERIFYID");
                checkNodeInfo.CheckStartId = GetXmlAttributeValue(firstRow, "CHECKSTARTID");
                checkNodeInfo.PhotoerId = GetXmlAttributeValue(firstRow, "PHOTOERID");
                checkNodeInfo.CriticalerId = GetXmlAttributeValue(firstRow, "CRITICALERID");
                checkNodeInfo.RptCommitDoc = GetXmlAttributeValue(firstRow, "RPTCOMMITDOC");
                checkNodeInfo.RptEndDoc = GetXmlAttributeValue(firstRow, "RPTENDDOC");

                // 解析时间字段
                checkNodeInfo.SeperateTime = ParseDateTime(GetXmlAttributeValue(firstRow, "SEPERATETIME"));
                checkNodeInfo.CheckVerifyTime = ParseDateTime(GetXmlAttributeValue(firstRow, "CHECKVERIFYTIME"));
                checkNodeInfo.CheckStartTime = ParseDateTime(GetXmlAttributeValue(firstRow, "CHECKSTARTTIME"));
                checkNodeInfo.StudyTime = ParseDateTime(GetXmlAttributeValue(firstRow, "STUDYTIME"));
                checkNodeInfo.CriticalTime = ParseDateTime(GetXmlAttributeValue(firstRow, "CRITICALTIME"));
                checkNodeInfo.RptCommitTime = ParseDateTime(GetXmlAttributeValue(firstRow, "RPTCOMMITTIME"));
                checkNodeInfo.RptEndTime = ParseDateTime(GetXmlAttributeValue(firstRow, "RPTENDTIME"));

                System.Diagnostics.Debug.WriteLine($"解析检查节点信息成功：患者{checkNodeInfo.PatientName}");
                return checkNodeInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析检查节点信息XML失败：{ex.Message}");
                throw new Exception($"解析检查节点信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取XML节点值
        /// </summary>
        /// <param name="parentNode">父节点</param>
        /// <param name="nodeName">节点名称</param>
        /// <returns>节点值</returns>
        private string GetXmlNodeValue(XmlNode parentNode, string nodeName)
        {
            XmlNode node = parentNode.SelectSingleNode(nodeName);
            return node?.InnerText ?? "";
        }

        /// <summary>
        /// 解析日期时间字符串
        /// </summary>
        /// <param name="dateTimeString">日期时间字符串</param>
        /// <returns>DateTime对象，解析失败返回null</returns>
        private DateTime? ParseDateTime(string dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString))
                return null;

            if (DateTime.TryParse(dateTimeString, out DateTime result))
                return result;

            return null;
        }

        /// <summary>
        /// 将检查节点信息转换为显示项列表
        /// </summary>
        /// <param name="checkNodeInfo">检查节点信息</param>
        /// <returns>显示项列表</returns>
        public List<CheckNodeDisplayItem> ConvertToDisplayItems(CheckNodeInfo checkNodeInfo)
        {
            if (checkNodeInfo == null)
                return new List<CheckNodeDisplayItem>();

            var displayItems = new List<CheckNodeDisplayItem>();

            // 分诊时间
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "分诊时间",
                OperatorName = checkNodeInfo.SeperateId ?? "-",
                OperateTime = checkNodeInfo.SeperateTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.SeperateTime.HasValue,
                IsCurrent = false
            });

            // 入室核对
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "入室核对",
                OperatorName = checkNodeInfo.CheckVerifyId ?? "-",
                OperateTime = checkNodeInfo.CheckVerifyTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.CheckVerifyTime.HasValue,
                IsCurrent = false
            });

            // 检查开始
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "检查开始",
                OperatorName = checkNodeInfo.CheckStartId ?? "-",
                OperateTime = checkNodeInfo.CheckStartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.CheckStartTime.HasValue,
                IsCurrent = false
            });

            // 检查完成
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "检查完成",
                OperatorName = checkNodeInfo.PhotoerId ?? "-",
                OperateTime = checkNodeInfo.StudyTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.StudyTime.HasValue,
                IsCurrent = false
            });

            // 上传结束（这里假设检查完成后就是上传结束，可以根据实际情况调整）
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "上传结束",
                OperatorName = "系统自动",
                OperateTime = checkNodeInfo.StudyTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.StudyTime.HasValue,
                IsCurrent = false
            });

            // 报告提交
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "报告提交",
                OperatorName = checkNodeInfo.RptCommitDoc ?? "-",
                OperateTime = checkNodeInfo.RptCommitTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.RptCommitTime.HasValue,
                IsCurrent = false
            });

            // 报告审核
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "报告审核",
                OperatorName = checkNodeInfo.RptEndDoc ?? "-",
                OperateTime = checkNodeInfo.RptEndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.RptEndTime.HasValue,
                IsCurrent = false
            });

            // 危急值上报
            displayItems.Add(new CheckNodeDisplayItem
            {
                NodeName = "危急值上报",
                OperatorName = checkNodeInfo.CriticalerId ?? "-",
                OperateTime = checkNodeInfo.CriticalTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.CriticalTime.HasValue,
                IsCurrent = false
            });

            return displayItems;
        }
    }
}
