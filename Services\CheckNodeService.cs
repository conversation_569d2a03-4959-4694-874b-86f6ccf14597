using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Xml;
using vocabulary.Models;

namespace vocabulary.Services
{
    /// <summary>
    /// 检查节点信息服务
    /// </summary>
    public class CheckNodeService
    {
        private readonly DatabaseService _databaseService;

        public CheckNodeService()
        {
            _databaseService = new DatabaseService();
        }

        /// <summary>
        /// 获取检查节点信息
        /// </summary>
        /// <param name="checkSerialNum">检查流水号</param>
        /// <returns>检查节点信息</returns>
        public async Task<CheckNodeInfo> GetCheckNodeInfoAsync(string checkSerialNum)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始查询检查节点信息，检查号：{checkSerialNum}");

                string sql = $@"
                    select t.devicetypeid,
                           t.patientname,
                           t.sex,
                           t.age,
                           t.patientype,
                           t.studyscription,
                           t.seperatetime,
                           t.seperateID,
                           t.checkverifytime,
                           t.checkverifyid,
                           t.checkstarttime,
                           t.checkstartid,
                           t.studytime,
                           t.photoerid,
                           t.criticaltime,
                           t.criticalerID,
                           t.rptcommittime,
                           t.rptcommitdoc,
                           t.rptendtime,
                           t.rptenddoc
                      from view_pacs_checkbnode t
                     where t.checkserialnum = '{checkSerialNum}'";

                System.Diagnostics.Debug.WriteLine($"检查节点查询SQL：{sql}");

                string result = await _databaseService.ExecuteQueryAsync(sql);
                System.Diagnostics.Debug.WriteLine($"检查节点查询结果：{result}");

                return ParseCheckNodeInfoFromXml(result);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查询检查节点信息失败：{ex.Message}");
                throw new Exception($"查询检查节点信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 从XML结果解析检查节点信息
        /// </summary>
        /// <param name="xmlResult">XML查询结果</param>
        /// <returns>检查节点信息</returns>
        private CheckNodeInfo ParseCheckNodeInfoFromXml(string xmlResult)
        {
            try
            {
                if (string.IsNullOrEmpty(xmlResult))
                {
                    System.Diagnostics.Debug.WriteLine("XML结果为空");
                    return null;
                }

                XmlDocument doc = new XmlDocument();
                doc.LoadXml(xmlResult);

                // 创建命名空间管理器
                XmlNamespaceManager nsmgr = new XmlNamespaceManager(doc.NameTable);
                nsmgr.AddNamespace("rs", "urn:schemas-microsoft-com:rowset");
                nsmgr.AddNamespace("z", "#RowsetSchema");

                // 查找rs:data节点
                XmlNode dataNode = doc.SelectSingleNode("//rs:data", nsmgr);
                if (dataNode == null)
                {
                    System.Diagnostics.Debug.WriteLine("未找到rs:data节点");
                    return null;
                }

                // 查找z:row节点
                XmlNodeList rowNodes = dataNode.SelectNodes("z:row", nsmgr);
                if (rowNodes == null || rowNodes.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("未找到z:row数据行");
                    return null;
                }

                XmlNode firstRow = rowNodes[0];
                CheckNodeInfo checkNodeInfo = new CheckNodeInfo();

                // 解析各个字段（从属性中获取值）
                checkNodeInfo.DeviceTypeId = GetXmlAttributeValue(firstRow, "DEVICETYPEID");
                checkNodeInfo.PatientName = GetXmlAttributeValue(firstRow, "PATIENTNAME");
                checkNodeInfo.Sex = GetXmlAttributeValue(firstRow, "SEX");
                checkNodeInfo.Age = GetXmlAttributeValue(firstRow, "AGE");
                checkNodeInfo.PatientType = GetXmlAttributeValue(firstRow, "PATIENTYPE");
                checkNodeInfo.StudyScription = GetXmlAttributeValue(firstRow, "STUDYSCRIPTION");
                checkNodeInfo.SeperateId = GetXmlAttributeValue(firstRow, "SEPERATEID");
                checkNodeInfo.CheckVerifyId = GetXmlAttributeValue(firstRow, "CHECKVERIFYID");
                checkNodeInfo.CheckStartId = GetXmlAttributeValue(firstRow, "CHECKSTARTID");
                checkNodeInfo.PhotoerId = GetXmlAttributeValue(firstRow, "PHOTOERID");
                checkNodeInfo.CriticalerId = GetXmlAttributeValue(firstRow, "CRITICALERID");
                checkNodeInfo.RptCommitDoc = GetXmlAttributeValue(firstRow, "RPTCOMMITDOC");
                checkNodeInfo.RptEndDoc = GetXmlAttributeValue(firstRow, "RPTENDDOC");

                // 解析时间字段
                checkNodeInfo.SeperateTime = ParseDateTime(GetXmlAttributeValue(firstRow, "SEPERATETIME"));
                checkNodeInfo.CheckVerifyTime = ParseDateTime(GetXmlAttributeValue(firstRow, "CHECKVERIFYTIME"));
                checkNodeInfo.CheckStartTime = ParseDateTime(GetXmlAttributeValue(firstRow, "CHECKSTARTTIME"));
                checkNodeInfo.StudyTime = ParseDateTime(GetXmlAttributeValue(firstRow, "STUDYTIME"));
                checkNodeInfo.CriticalTime = ParseDateTime(GetXmlAttributeValue(firstRow, "CRITICALTIME"));
                checkNodeInfo.RptCommitTime = ParseDateTime(GetXmlAttributeValue(firstRow, "RPTCOMMITTIME"));
                checkNodeInfo.RptEndTime = ParseDateTime(GetXmlAttributeValue(firstRow, "RPTENDTIME"));

                System.Diagnostics.Debug.WriteLine($"解析检查节点信息成功：患者{checkNodeInfo.PatientName}");
                return checkNodeInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析检查节点信息XML失败：{ex.Message}");
                throw new Exception($"解析检查节点信息失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取XML节点值
        /// </summary>
        /// <param name="parentNode">父节点</param>
        /// <param name="nodeName">节点名称</param>
        /// <returns>节点值</returns>
        private string GetXmlNodeValue(XmlNode parentNode, string nodeName)
        {
            XmlNode node = parentNode.SelectSingleNode(nodeName);
            return node?.InnerText ?? "";
        }

        /// <summary>
        /// 获取XML属性值
        /// </summary>
        /// <param name="node">XML节点</param>
        /// <param name="attributeName">属性名称</param>
        /// <returns>属性值</returns>
        private string GetXmlAttributeValue(XmlNode node, string attributeName)
        {
            XmlAttribute attr = node.Attributes?[attributeName];
            return attr?.Value ?? "";
        }

        /// <summary>
        /// 解析日期时间字符串
        /// </summary>
        /// <param name="dateTimeString">日期时间字符串</param>
        /// <returns>DateTime对象，解析失败返回null</returns>
        private DateTime? ParseDateTime(string dateTimeString)
        {
            if (string.IsNullOrEmpty(dateTimeString))
                return null;

            if (DateTime.TryParse(dateTimeString, out DateTime result))
                return result;

            return null;
        }

        /// <summary>
        /// 将检查节点信息转换为显示项列表
        /// </summary>
        /// <param name="checkNodeInfo">检查节点信息</param>
        /// <returns>显示项列表，包含第一行和第二行的节点</returns>
        public CheckNodeDisplayResult ConvertToDisplayItems(CheckNodeInfo checkNodeInfo)
        {
            var result = new CheckNodeDisplayResult();

            if (checkNodeInfo == null)
                return result;

            // 第一行节点：分诊时间 → 入室核对 → 检查开始 → 检查完成
            result.FirstRow.Add(new CheckNodeDisplayItem
            {
                NodeName = "分诊时间",
                OperatorName = checkNodeInfo.SeperateId ?? "-",
                OperateTime = checkNodeInfo.SeperateTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.SeperateTime.HasValue,
                IsCurrent = false
            });

            result.FirstRow.Add(new CheckNodeDisplayItem
            {
                NodeName = "入室核对",
                OperatorName = checkNodeInfo.CheckVerifyId ?? "-",
                OperateTime = checkNodeInfo.CheckVerifyTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.CheckVerifyTime.HasValue,
                IsCurrent = false
            });

            result.FirstRow.Add(new CheckNodeDisplayItem
            {
                NodeName = "检查开始",
                OperatorName = checkNodeInfo.CheckStartId ?? "-",
                OperateTime = checkNodeInfo.CheckStartTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.CheckStartTime.HasValue,
                IsCurrent = false
            });

            result.FirstRow.Add(new CheckNodeDisplayItem
            {
                NodeName = "检查完成",
                OperatorName = checkNodeInfo.PhotoerId ?? "-",
                OperateTime = checkNodeInfo.StudyTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.StudyTime.HasValue,
                IsCurrent = false
            });

            // 创建三个节点
            var criticalNode = new CheckNodeDisplayItem
            {
                NodeName = "危急值上报",
                OperatorName = checkNodeInfo.CriticalerId ?? "-",
                OperateTime = checkNodeInfo.CriticalTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.CriticalTime.HasValue,
                IsCurrent = false
            };

            var reportCommitNode = new CheckNodeDisplayItem
            {
                NodeName = "报告提交",
                OperatorName = checkNodeInfo.RptCommitDoc ?? "-",
                OperateTime = checkNodeInfo.RptCommitTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.RptCommitTime.HasValue,
                IsCurrent = false
            };

            var reportAuditNode = new CheckNodeDisplayItem
            {
                NodeName = "报告审核",
                OperatorName = checkNodeInfo.RptEndDoc ?? "-",
                OperateTime = checkNodeInfo.RptEndTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? "-",
                IsCompleted = checkNodeInfo.RptEndTime.HasValue,
                IsCurrent = false
            };

            // 检查哪些节点有数据
            bool hasCritical = checkNodeInfo.CriticalTime.HasValue;
            bool hasReportCommit = checkNodeInfo.RptCommitTime.HasValue;
            bool hasReportAudit = checkNodeInfo.RptEndTime.HasValue;

            // 根据三个节点的时间进行动态排序
            var reportNodes = new List<NodeSortItem>();

            if (hasCritical)
                reportNodes.Add(new NodeSortItem(criticalNode, checkNodeInfo.CriticalTime, "critical"));
            if (hasReportCommit)
                reportNodes.Add(new NodeSortItem(reportCommitNode, checkNodeInfo.RptCommitTime, "commit"));
            if (hasReportAudit)
                reportNodes.Add(new NodeSortItem(reportAuditNode, checkNodeInfo.RptEndTime, "audit"));

            // 按时间排序，但确保报告审核不会在报告提交之前
            var sortedNodes = reportNodes.OrderBy(x => x.Time ?? DateTime.MaxValue).ToList();

            // 特殊处理：如果报告审核在报告提交之前，需要调整顺序
            var commitIndex = sortedNodes.FindIndex(x => x.Type == "commit");
            var auditIndex = sortedNodes.FindIndex(x => x.Type == "audit");

            if (commitIndex >= 0 && auditIndex >= 0 && auditIndex < commitIndex)
            {
                // 报告审核在报告提交之前，需要调整
                var auditNode = sortedNodes[auditIndex];
                sortedNodes.RemoveAt(auditIndex);

                // 将审核节点放在提交节点之后
                if (commitIndex < sortedNodes.Count)
                    sortedNodes.Insert(commitIndex + 1, auditNode);
                else
                    sortedNodes.Add(auditNode);
            }

            // 根据排序结果分配到第一行和第二行
            if (sortedNodes.Count == 0)
            {
                // 没有任何节点，不添加任何内容
            }
            else if (sortedNodes.Count == 1)
            {
                // 只有一个节点，放在第一行
                result.FirstRow.Add(sortedNodes[0].Node);
            }
            else if (sortedNodes.Count == 2)
            {
                // 两个节点，第一个放第一行，第二个放第二行
                result.FirstRow.Add(sortedNodes[0].Node);
                result.SecondRow.Add(sortedNodes[1].Node);
            }
            else if (sortedNodes.Count == 3)
            {
                // 三个节点，第一个放第一行，后两个放第二行
                result.FirstRow.Add(sortedNodes[0].Node);
                result.SecondRow.Add(sortedNodes[1].Node);
                result.SecondRow.Add(sortedNodes[2].Node);
            }

            return result;
        }
    }
}
