<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查流程节点信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        success: '#36D399',
                        neutral: '#94A3B8',
                        background: '#F8FAFC',
                        card: '#FFFFFF',
                        text: {
                            primary: '#1E293B',
                            secondary: '#64748B'
                        }
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .timeline-node {
                @apply flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300 z-10;
            }
            .timeline-node-completed {
                @apply bg-primary text-white;
            }
            .timeline-node-current {
                @apply bg-primary text-white scale-110;
            }
            .timeline-node-pending {
                @apply bg-neutral text-white;
            }
            .node-label {
                @apply text-sm font-medium text-text-primary mb-2;
            }
            .node-meta {
                @apply text-xs text-text-secondary mt-2 w-24 text-center;
            }
            .horizontal-line {
                @apply h-0.5 bg-neutral w-16 mx-2 my-auto;
            }
            .timeline-container {
                @apply relative w-full px-10;
            }
            /* 审批流布局 */
            .workflow-container {
                @apply flex flex-col;
                padding-right: 90px;
            }
            .workflow-row {
                @apply flex items-center mb-8;
            }
            .workflow-row.first-row {
                @apply justify-end;
            }
            .workflow-row.second-row {
                @apply justify-end;
            }
            .workflow-node {
                @apply flex flex-col items-center;
                width: 120px;
            }
            .workflow-circle {
                @apply flex items-center justify-center w-12 h-12 rounded-full transition-all duration-300 mb-2;
            }
            .workflow-circle.completed {
                @apply bg-primary text-white;
            }
            .workflow-circle.pending {
                @apply bg-neutral text-white;
            }
            .workflow-label {
                @apply text-sm font-medium text-text-primary text-center mb-1;
            }
            .workflow-meta {
                @apply text-xs text-text-secondary text-center;
            }
            .workflow-line {
                @apply h-0.5 bg-neutral mx-2;
                width: 80px;
            }
            .workflow-vertical-line {
                @apply w-0.5 bg-neutral h-8 mb-4;
                margin-left: calc(100% - 60px);
            }
        }
    </style>
</head>
<body class="font-inter bg-background min-h-screen flex items-center justify-center p-4 overflow-hidden">
    <!-- 主容器 -->
    <div class="w-[1200px] h-[700px] bg-card rounded-xl shadow-lg overflow-hidden flex flex-col">
        <!-- 标题栏 -->
        <header class="bg-white text-text-primary px-8 py-3 flex items-center justify-between border-b border-gray-200">
            <h1 class="text-[clamp(1.1rem,2vw,1.5rem)] font-bold">
                检查流程节点信息
            </h1>
        </header>
        
        <!-- 内容区域 -->
        <main class="flex-1 p-6 overflow-hidden">
            <!-- 患者信息卡片 -->
            <div class="bg-background rounded-lg p-4 mb-6 shadow-sm border border-gray-100">
                <h2 class="text-lg font-semibold text-text-primary mb-4">
                    患者信息
                </h2>
                <div class="space-y-3">
                    <!-- 第一行：姓名、性别、年龄、检查类型 -->
                    <div class="flex gap-16 text-base">
                        <div>
                            <span class="text-text-secondary">姓名: </span>
                            <span class="font-medium">张三</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">性别: </span>
                            <span class="font-medium">男</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">年龄: </span>
                            <span class="font-medium">45岁</span>
                        </div>
                        <div>
                            <span class="text-text-secondary">检查类型: </span>
                            <span class="font-medium">CT扫描</span>
                        </div>
                    </div>
                    <!-- 第二行：检查部位 -->
                    <div class="text-base">
                        <span class="text-text-secondary">检查部位: </span>
                        <span class="font-medium">胸部</span>
                    </div>
                </div>
            </div>
            
            <!-- 审批流容器 -->
            <div class="workflow-container" style="margin-top: 50px;">
                <!-- 第一行：5个节点 -->
                <div class="workflow-row first-row">
                    <!-- 分诊时间 -->
                    <div class="workflow-node">
                        <div class="workflow-circle completed">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="workflow-label">分诊时间</div>
                        <div class="workflow-meta">
                            <div>王护士</div>
                            <div>2023-08-03 08:15:00</div>
                        </div>
                    </div>

                    <div class="workflow-line"></div>

                    <!-- 入室核对 -->
                    <div class="workflow-node">
                        <div class="workflow-circle completed">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="workflow-label">入室核对</div>
                        <div class="workflow-meta">
                            <div>李护士</div>
                            <div>2023-08-03 08:45:00</div>
                        </div>
                    </div>

                    <div class="workflow-line"></div>

                    <!-- 检查开始 -->
                    <div class="workflow-node">
                        <div class="workflow-circle completed">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="workflow-label">检查开始</div>
                        <div class="workflow-meta">
                            <div>赵医生</div>
                            <div>2023-08-03 09:00:00</div>
                        </div>
                    </div>

                    <div class="workflow-line"></div>

                    <!-- 检查时间 -->
                    <div class="workflow-node">
                        <div class="workflow-circle completed">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="workflow-label">检查完成</div>
                        <div class="workflow-meta">
                            <div>赵医生</div>
                            <div>2023-08-03 09:05:00</div>
                        </div>
                    </div>

                    <div class="workflow-line"></div>

                    <!-- 上传结束 -->
                    <div class="workflow-node">
                        <div class="workflow-circle completed">
                            <i class="fa fa-check"></i>
                        </div>
                        <div class="workflow-label">上传结束</div>
                        <div class="workflow-meta">
                            <div>系统自动</div>
                            <div>2023-08-03 09:35:00</div>
                        </div>
                    </div>
                </div>

                <!-- 垂直连接线 -->
                <div class="workflow-vertical-line"></div>

                <!-- 第二行：3个节点 -->
                <div class="workflow-row second-row">
                    <!-- 空白占位，确保对齐 -->
                    <div style="width: 240px;"></div>

                    <!-- 报告审核 -->
                    <div class="workflow-node">
                        <div class="workflow-circle pending">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="workflow-label">报告审核</div>
                        <div class="workflow-meta">
                            <div>-</div>
                            <div>-</div>
                        </div>
                    </div>

                    <div class="workflow-line"></div>

                    <!-- 报告提交 -->
                    <div class="workflow-node">
                        <div class="workflow-circle pending">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="workflow-label">报告提交</div>
                        <div class="workflow-meta">
                            <div>-</div>
                            <div>-</div>
                        </div>
                    </div>

                    <div class="workflow-line"></div>

                    <!-- 危急值上报 -->
                    <div class="workflow-node">
                        <div class="workflow-circle pending">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="workflow-label">危急值上报</div>
                        <div class="workflow-meta">
                            <div>-</div>
                            <div>-</div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        

    </div>    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // 移除了垂直连接线相关的偏移计算代码
            // 确保所有水平连接线具有完全相同的固定长度（已通过CSS固定）
        });
    </script>
</body>
</html>