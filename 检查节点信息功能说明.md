# 检查节点信息功能说明

## 功能概述

新增了检查流程节点信息查询页面，用于展示患者检查过程中各个关键节点的时间和操作人信息。

## 启动方式

使用命令行参数启动：
```
ConvergePACS.exe checknode#检查流水号
```

例如：
```
ConvergePACS.exe checknode#20250101001234
```

## 功能特性

### 1. 患者信息展示
- 患者姓名
- 性别
- 年龄  
- 检查类型（设备类型）
- 检查部位

### 2. 检查流程节点时间线

#### 第一行节点（5个）：
1. **分诊时间** - 显示预约/分诊时间和操作人
2. **入室核对** - 显示入室核对时间和核对人
3. **检查开始** - 显示检查开始时间和操作人
4. **检查完成** - 显示检查完成时间和操作人
5. **上传结束** - 显示上传结束时间（系统自动）

#### 第二行节点（3个）：
1. **报告提交** - 显示报告提交时间和报告医生
2. **报告审核** - 显示报告审核时间和审核医生
3. **危急值上报** - 显示危急值上报时间和上报人

### 3. 节点状态显示
- **已完成节点**：蓝色圆圈，显示✓图标
- **未完成节点**：灰色圆圈，显示○图标
- 每个节点显示操作人和操作时间

## 数据源

使用数据库视图 `view_pacs_checkbnode` 查询数据，SQL语句：

```sql
select t.devicetypeid,--设备类型
       t.patientname,---患者姓名
       t.sex,--性别
       t.age,--年龄
       t.studyscription,--检查部位
       t.seperatetime,--预约/分诊时间
       t.seperateID,--分诊操作人
       t.checkverifytime,--入室核对时间
       t.checkverifyid,--入室核对人
       t.checkstarttime,--检查开始时间
       t.checkstartid, --检查开始人
       t.studytime, --检查完成时间
       t.photoerid,--检查完成人
       t.criticaltime,--危急值上报时间
       t.criticalerID,--危急值上报人
       t.rptcommittime,--报告时间
       t.rptcommitdoc,--报告医生
       t.rptendtime,--审核时间
       t.rptenddoc  --审核医生
  from view_pacs_checkbnode t 
 where t.checkserialnum='传入的检查流水号'
```

## 技术实现

### 文件结构
- `CheckNodeWindow.xaml` - 界面布局文件
- `CheckNodeWindow.xaml.cs` - 界面逻辑代码
- `Services/CheckNodeService.cs` - 数据查询服务
- `Models/PatientInfo.cs` - 数据模型（新增CheckNodeInfo和CheckNodeDisplayItem）

### 主要类说明

#### CheckNodeInfo
检查节点信息数据模型，包含所有检查流程节点的时间和操作人信息。

#### CheckNodeDisplayItem  
用于界面显示的节点项模型，包含节点名称、操作人、操作时间和完成状态。

#### CheckNodeService
提供检查节点信息的查询和数据转换功能：
- `GetCheckNodeInfoAsync()` - 异步查询检查节点信息
- `ConvertToDisplayItems()` - 将数据转换为界面显示项

## 界面设计

界面采用卡片式设计，包含：
1. **标题栏** - 显示"检查流程节点信息"
2. **患者信息卡片** - 展示患者基本信息
3. **流程时间线** - 以流程图形式展示各个检查节点

时间线采用两行布局：
- 第一行：5个基础检查节点
- 第二行：3个报告相关节点
- 使用连接线和垂直线连接各个节点

## 错误处理

- 如果检查流水号不存在，显示错误提示并关闭窗口
- 数据库连接失败时显示相应错误信息
- 解析XML数据失败时提供详细错误信息

## 兼容性

- 基于.NET Framework 4.5.2
- 使用WPF技术实现界面
- 通过HTTP接口与数据库通信
- 支持GBK编码的数据库响应
