using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using System.IO;
using System.Threading.Tasks;
using System.Xml;
using System.Runtime.InteropServices;
using vocabulary.Models;
using vocabulary.Services;
using vocabulary.Utils;

namespace vocabulary
{
    /// <summary>
    /// 队列验证结果枚举
    /// </summary>
    public enum QueueVerificationResult
    {
        Success,           // 验证成功
        PatientIdMismatch, // 患者编号不匹配
        QueueNotFound,     // 队列中未找到患者
        InfoMismatch       // 队列信息不匹配
    }

    /// <summary>
    /// Windows API 用于强制获取焦点
    /// </summary>
    public static class WindowsApi
    {
        [DllImport("user32.dll")]
        public static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        public static extern bool SetFocus(IntPtr hWnd);

        [DllImport("user32.dll")]
        public static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        public static extern bool AttachThreadInput(uint idAttach, uint idAttachTo, bool fAttach);

        [DllImport("user32.dll")]
        public static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        [DllImport("kernel32.dll")]
        public static extern uint GetCurrentThreadId();

        public const int SW_SHOW = 5;
        public const int SW_RESTORE = 9;
    }

    /// <summary>
    /// WPF窗口句柄获取辅助类
    /// </summary>
    public static class WindowHelper
    {
        [DllImport("user32.dll")]
        public static extern IntPtr GetWindow(IntPtr hWnd, uint uCmd);

        public const uint GW_CHILD = 5;

        /// <summary>
        /// 获取WPF窗口的句柄
        /// </summary>
        public static IntPtr GetWindowHandle(Window window)
        {
            var helper = new System.Windows.Interop.WindowInteropHelper(window);
            return helper.Handle;
        }
    }

    /// <summary>
    /// PatientCheckWindow.xaml 的交互逻辑
    /// </summary>
    public partial class PatientCheckWindow : Window
    {
        private bool _isVerified = false;
        private string _verifierName = "";
        private DateTime _verificationTime;
        private PatientDataService _dataService;
        private PatientCheckInfo _currentPatientInfo;
        private string _currentUserId;
        private string _checkSerialNum;
        private string _verifyInfo;
        private bool _verificationSuccess = false;
        private bool _verificationFailed = false;
        private DispatcherTimer _countdownTimer;
        private int _countdownSeconds;
        private DispatcherTimer _focusTimer;
        private bool _checkStartCompleted = false;

        public PatientCheckWindow()
        {
            InitializeComponent();

            // 窗口加载完成后设置自定义标题栏文本和窗口位置
            this.Loaded += (s, e) => {
                TitleTextBlock.Text = "影像系统入室核对";
                // 在窗口完全加载后设置位置，确保Height已正确计算
                SetWindowPosition();
            };

            // 解析命令行参数
            ParseCommandLineArguments();

            // 初始化数据服务
            try
            {
                _dataService = new PatientDataService();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化数据服务失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown(1);
                return;
            }

            // 加载患者信息
            LoadPatientInfoAsync();

            // 设置焦点到输入框
            this.Loaded += PatientCheckWindow_Loaded;

            // 改善文本显示质量（.NET 4.5.2兼容方式）
            this.SetValue(RenderOptions.BitmapScalingModeProperty, BitmapScalingMode.HighQuality);

            // 启动1.5秒后重新获取焦点的定时器
            StartFocusTimer();
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        private void PatientCheckWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // 窗口加载完成后设置焦点
            this.Dispatcher.BeginInvoke(new Action(() =>
            {
                ScanInputTextBox.Focus();
                Keyboard.Focus(ScanInputTextBox);
            }), DispatcherPriority.Input);
        }

        /// <summary>
        /// 设置窗口位置
        /// </summary>
        private void SetWindowPosition()
        {
            try
            {
                // 从配置文件读取窗口位置
                string configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "WindowPosition.ini");

                if (File.Exists(configPath))
                {
                    string[] lines = File.ReadAllLines(configPath);
                    bool leftSet = false, topSet = false;

                    foreach (string line in lines)
                    {
                        if (line.StartsWith("Left="))
                        {
                            if (double.TryParse(line.Substring(5), out double left))
                            {
                                // 确保窗口不会超出屏幕边界
                                if (left >= 0 && left <= SystemParameters.PrimaryScreenWidth - this.Width)
                                {
                                    this.Left = left;
                                    leftSet = true;
                                }
                            }
                        }
                        else if (line.StartsWith("Top="))
                        {
                            if (double.TryParse(line.Substring(4), out double top))
                            {
                                // 确保窗口不会超出屏幕边界
                                if (top >= 0 && top <= SystemParameters.PrimaryScreenHeight - this.Height)
                                {
                                    this.Top = top;
                                    topSet = true;
                                }
                            }
                        }
                    }

                    // 如果配置文件中的位置无效，使用默认位置
                    if (!leftSet)
                    {
                        this.Left = 50;
                    }
                    if (!topSet)
                    {
                        this.Top = (SystemParameters.PrimaryScreenHeight - this.Height) / 2;
                    }
                }
                else
                {
                    // 默认靠左显示
                    this.Left = 50;
                    this.Top = (SystemParameters.PrimaryScreenHeight - this.Height) / 2;
                }
            }
            catch
            {
                // 如果读取失败，使用默认位置
                this.Left = 50;
                this.Top = (SystemParameters.PrimaryScreenHeight - this.Height) / 2;
            }
        }

        /// <summary>
        /// 保存窗口位置
        /// </summary>
        private void SaveWindowPosition()
        {
            try
            {
                string configPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "WindowPosition.ini");
                string content = $"Left={this.Left}\nTop={this.Top}";
                File.WriteAllText(configPath, content);
            }
            catch
            {
                // 忽略保存失败的错误
            }
        }

        /// <summary>
        /// 启动焦点定时器
        /// </summary>
        private void StartFocusTimer()
        {
            _focusTimer = new DispatcherTimer();
            _focusTimer.Interval = TimeSpan.FromSeconds(1.5);
            _focusTimer.Tick += FocusTimer_Tick;
            _focusTimer.Start();
        }

        /// <summary>
        /// 焦点定时器事件
        /// </summary>
        private void FocusTimer_Tick(object sender, EventArgs e)
        {
            _focusTimer.Stop();

            // 使用Windows API强制重新获取焦点
            ForceWindowToForeground();
        }

        /// <summary>
        /// 强制窗口获得前台焦点
        /// </summary>
        private void ForceWindowToForeground()
        {
            try
            {
                IntPtr windowHandle = WindowHelper.GetWindowHandle(this);
                if (windowHandle == IntPtr.Zero)
                {
                    // 如果无法获取句柄，使用传统方法
                    FallbackFocusMethod();
                    return;
                }

                // 获取当前前台窗口
                IntPtr foregroundWindow = WindowsApi.GetForegroundWindow();

                if (foregroundWindow != windowHandle)
                {
                    // 获取当前前台窗口的线程ID
                    uint foregroundThreadId = WindowsApi.GetWindowThreadProcessId(foregroundWindow, out _);
                    uint currentThreadId = WindowsApi.GetCurrentThreadId();

                    // 如果不是同一个线程，需要附加线程输入
                    if (foregroundThreadId != currentThreadId)
                    {
                        WindowsApi.AttachThreadInput(currentThreadId, foregroundThreadId, true);

                        // 强制设置前台窗口
                        WindowsApi.SetForegroundWindow(windowHandle);
                        WindowsApi.ShowWindow(windowHandle, WindowsApi.SW_SHOW);

                        // 分离线程输入
                        WindowsApi.AttachThreadInput(currentThreadId, foregroundThreadId, false);
                    }
                    else
                    {
                        // 同一线程，直接设置前台窗口
                        WindowsApi.SetForegroundWindow(windowHandle);
                    }
                }

                // 确保窗口激活并设置焦点到输入框
                this.Dispatcher.BeginInvoke(new Action(() =>
                {
                    this.Activate();
                    this.Focus();

                    // 设置输入框焦点
                    ScanInputTextBox.Focus();
                    Keyboard.Focus(ScanInputTextBox);

                    // 将光标移到文本末尾
                    if (!string.IsNullOrEmpty(ScanInputTextBox.Text))
                    {
                        ScanInputTextBox.Select(ScanInputTextBox.Text.Length, 0);
                    }

                    // 最后一次确保焦点
                    this.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        if (!ScanInputTextBox.IsKeyboardFocused)
                        {
                            Keyboard.Focus(ScanInputTextBox);
                        }
                    }), DispatcherPriority.ApplicationIdle);

                }), DispatcherPriority.Input);
            }
            catch
            {
                // 如果Windows API调用失败，使用传统方法
                FallbackFocusMethod();
            }
        }

        /// <summary>
        /// 备用焦点设置方法
        /// </summary>
        private void FallbackFocusMethod()
        {
            try
            {
                // 先将窗口置顶并激活
                this.Topmost = false;
                this.Topmost = true;
                this.Activate();
                this.Focus();

                // 设置输入框焦点
                this.Dispatcher.BeginInvoke(new Action(() =>
                {
                    ScanInputTextBox.Focus();
                    Keyboard.Focus(ScanInputTextBox);

                    if (!string.IsNullOrEmpty(ScanInputTextBox.Text))
                    {
                        ScanInputTextBox.Select(ScanInputTextBox.Text.Length, 0);
                    }
                }), DispatcherPriority.Input);
            }
            catch
            {
                // 最简单的焦点设置
                ScanInputTextBox.Focus();
            }
        }

        /// <summary>
        /// 解析命令行参数
        /// </summary>
        private void ParseCommandLineArguments()
        {
            string[] args = Environment.GetCommandLineArgs();

            if (args.Length <= 1)
            {
                MessageBox.Show("缺少必要的启动参数！",
                    "参数错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown(1);
                return;
            }

            // 新的参数格式：VerifyInfo#用户ID#检查流水号
            string[] parts = args[1].Split('#');
            if (parts.Length != 3 || string.IsNullOrWhiteSpace(parts[0]) || 
                string.IsNullOrWhiteSpace(parts[1]) || string.IsNullOrWhiteSpace(parts[2]))
            {
                MessageBox.Show($"参数格式错误！应为：VerifyInfo#用户ID#检查流水号",
                    "参数错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown(1);
                return;
            }

            _verifyInfo = parts[0].Trim();
            _currentUserId = parts[1].Trim();
            _checkSerialNum = parts[2].Trim();

            // 验证第一个参数是否为"VerifyInfo"
            if (!_verifyInfo.Equals("VerifyInfo", StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show($"参数格式错误！第一个参数必须为'VerifyInfo'",
                    "参数错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown(1);
                return;
            }
        }

        /// <summary>
        /// 异步加载患者信息
        /// </summary>
        private async void LoadPatientInfoAsync()
        {
            try
            {
                // 显示加载状态
                PatientNameText.Text = "正在加载...";

                // 从数据库获取患者信息
                _currentPatientInfo = await _dataService.GetPatientCheckInfoAsync(_checkSerialNum, _currentUserId);

                if (_currentPatientInfo == null)
                {
                    MessageBox.Show($"未找到对应的患者信息！\n\n检查流水号：{_checkSerialNum}\n用户ID：{_currentUserId}",
                        "数据错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    Application.Current.Shutdown(1);
                    return;
                }

                // 更新界面显示
                UpdatePatientInfoDisplay();

                // 检查是否已经核对过
                CheckVerificationStatus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载患者信息失败：{ex.Message}", "数据库错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown(1);
            }
        }

        /// <summary>
        /// 更新患者信息显示
        /// </summary>
        private void UpdatePatientInfoDisplay()
        {
            if (_currentPatientInfo == null) return;

            PatientNameText.Text = _currentPatientInfo.PatientName ?? "";
            PatientGenderText.Text = _currentPatientInfo.SexAndAge ?? "";  // 使用合并的性别/年龄字段
            PatientIdText.Text = _currentPatientInfo.PatientId ?? "";
            OrderDepartmentText.Text = _currentPatientInfo.DeptAndBed ?? "";  // 使用合并的科室/床号字段
            QueueText.Text = _currentPatientInfo.QueueNo ?? "";  // 新增排队号字段
            ExamTypeText.Text = _currentPatientInfo.StudyScription ?? "";

            // 更新标题中的患者类型信息
            if (!string.IsNullOrEmpty(_currentPatientInfo.TypeName))
            {
                this.Title = $"影像系统入室核对 ({_currentPatientInfo.TypeName})";
            }

            // 更新状态栏的当前登录用户
            OperatorInfoText.Text = $"当前登录: {_currentPatientInfo.CurrentUser ?? "未知用户"}";
        }

        /// <summary>
        /// 检查核对状态
        /// </summary>
        private void CheckVerificationStatus()
        {
            if (_currentPatientInfo != null &&
                !string.IsNullOrEmpty(_currentPatientInfo.CheckVerifyId) &&
                _currentPatientInfo.CheckVerifyTime.HasValue)
            {
                // 已经核对过
                _isVerified = true;
                _verifierName = _currentPatientInfo.CheckVerifyId;
                _verificationTime = _currentPatientInfo.CheckVerifyTime.Value;

                // 禁用输入和按钮
                ScanInputTextBox.IsEnabled = false;
                VerifyButton.IsEnabled = false;
                ClearButton.IsEnabled = false;

                // 显示已核对的结果
                ShowAlreadyVerifiedResult();

                UpdateVerificationStatus();
            }
            else
            {
                InitializeVerificationStatus();
            }
        }

        /// <summary>
        /// 显示已核对的结果
        /// </summary>
        private void ShowAlreadyVerifiedResult()
        {
            // 显示结果区域
            VerificationResultBorder.Visibility = Visibility.Visible;

            // 设置为成功样式
            VerificationResultBorder.Background = new SolidColorBrush(Color.FromRgb(212, 237, 218));
            VerificationResultBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(195, 230, 203));
            VerificationResultBorder.BorderThickness = new Thickness(2);

            ResultIconText.Text = "✔";
            ResultIconText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));

            ResultMessageText.Text = $"[{_verifierName}]已核对，无需二次核对";
            ResultMessageText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));

            // 标记为已成功核对，但不自动关闭（因为是已经核对过的，不是当前核对操作）
            _verificationSuccess = true;
            // 已核对的患者不需要自动关闭
        }

        /// <summary>
        /// 初始化核对状态显示
        /// </summary>
        private void InitializeVerificationStatus()
        {
            // 确保警告区域初始时是隐藏的
            QueueWarningBorder.Visibility = Visibility.Collapsed;
            UpdateVerificationStatus();
        }

        /// <summary>
        /// 更新核对状态显示
        /// </summary>
        private void UpdateVerificationStatus()
        {
            if (_isVerified)
            {
                VerificationStatusText.Text = $"核对人：{_verifierName}  核对时间：{_verificationTime:yyyy-MM-dd HH:mm:ss}";
            }
            else
            {
                VerificationStatusText.Text = "状态：未核对";
            }
        }

        /// <summary>
        /// 标题栏鼠标拖拽事件
        /// </summary>
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ButtonState == MouseButtonState.Pressed)
            {
                this.DragMove();
                // 拖动完成后保存位置
                SaveWindowPosition();
            }
        }

        /// <summary>
        /// 最小化按钮点击事件
        /// </summary>
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 输入框键盘事件处理
        /// </summary>
        private void ScanInputTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            // 检查是否是Ctrl组合键，如果是则直接让系统处理，不做任何拦截
            if ((Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
            {
                // 对于所有Ctrl组合键（Ctrl+C, Ctrl+X, Ctrl+V, Ctrl+A等），完全不处理，让系统默认行为执行
                return;
            }

            // 只处理Enter键
            if (e.Key == Key.Enter)
            {
                // 使用Dispatcher.BeginInvoke确保在UI线程空闲时执行，避免阻塞剪切板操作
                Dispatcher.BeginInvoke(new Action(() => {
                    VerifyPatient();
                }), DispatcherPriority.Background);
                e.Handled = true;
            }
        }

        /// <summary>
        /// 核对按钮点击事件
        /// </summary>
        private void VerifyButton_Click(object sender, RoutedEventArgs e)
        {
            VerifyPatient();
        }

        /// <summary>
        /// 清空按钮点击事件
        /// </summary>
        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            ClearInput();
        }

        /// <summary>
        /// 检查开始按钮点击事件
        /// </summary>
        private void CheckStartButton_Click(object sender, RoutedEventArgs e)
        {
            _ = ExecuteCheckStartAsync();
        }

        /// <summary>
        /// 患者核对功能
        /// </summary>
        private async void VerifyPatient()
        {
            if (_currentPatientInfo == null)
            {
                MessageBox.Show("患者信息未加载完成，请稍后再试！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            string scannedId = ScanInputTextBox.Text.Trim();
            string expectedId = _currentPatientInfo.PatientId;

            if (string.IsNullOrEmpty(scannedId))
            {
                MessageBox.Show("请输入或扫描患者编号！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                ScanInputTextBox.Focus();
                return;
            }

            try
            {
                bool verificationResult = false;
                bool shouldShowQueueWarning = false;

                // 根据配置文件决定核对方式
                if (PatientCheckConfigHelper.GetLinkQueueConfig())
                {
                    // 启用队列验证，需要查询队列信息进行核对
                    QueueVerificationResult queueResult = await VerifyWithQueueInfoAsync(scannedId);

                    // 判断是否需要显示队列警告
                    shouldShowQueueWarning = (queueResult == QueueVerificationResult.QueueNotFound ||
                                            queueResult == QueueVerificationResult.InfoMismatch) &&
                                            (scannedId == _currentPatientInfo.PatientId);

                    // 修改逻辑：当患者编号匹配时，即使队列验证失败也算核对成功
                    if (scannedId == _currentPatientInfo.PatientId)
                    {
                        verificationResult = true; // 患者编号匹配就算成功
                    }
                    else
                    {
                        verificationResult = false; // 患者编号不匹配才算失败
                        shouldShowQueueWarning = false; // 编号不匹配时不显示警告
                    }
                }
                else
                {
                    // 不启用队列验证，直接比较患者编号
                    verificationResult = (scannedId == expectedId);
                    shouldShowQueueWarning = false; // 不使用队列验证时不显示警告
                }

                // 显示结果区域
                VerificationResultBorder.Visibility = Visibility.Visible;

                // 控制队列警告区域的显示
                QueueWarningBorder.Visibility = shouldShowQueueWarning ? Visibility.Visible : Visibility.Collapsed;

                if (verificationResult)
                {
                    // 核对成功，更新数据库
                    bool updateSuccess = await _dataService.UpdateVerificationAsync(_checkSerialNum, _currentUserId);

                    if (updateSuccess)
                    {
                        ShowVerificationSuccess();

                        // 更新核对状态
                        _isVerified = true;
                        _verifierName = _currentPatientInfo.CurrentUser ?? "未知用户";
                        _verificationTime = DateTime.Now;
                        UpdateVerificationStatus();

                        // 禁用输入和按钮
                        ScanInputTextBox.IsEnabled = false;
                        VerifyButton.IsEnabled = false;
                        ClearButton.IsEnabled = false;

                        // 标记核对成功
                        _verificationSuccess = true;

                        // 处理检查开始功能
                        await HandleCheckStartAsync();
                    }
                    else
                    {
                        ShowVerificationError("核对成功但保存失败，请联系管理员！");
                        _verificationFailed = true;
                    }
                }
                else
                {
                    // 核对失败
                    ShowVerificationError($"核对失败！患者号{scannedId}与当前患者不匹配！");
                    _verificationFailed = true;
                }
            }
            catch //(Exception ex)
            {
                ShowVerificationError($"核对过程中发生错误,请联系管理员！");//{ex.Message}
                _verificationFailed = true;
            }
        }

        /// <summary>
        /// 使用队列信息进行核对
        /// </summary>
        /// <param name="scannedId">扫描的患者编号</param>
        /// <returns>核对结果枚举</returns>
        private async Task<QueueVerificationResult> VerifyWithQueueInfoAsync(string scannedId)
        {
            try
            {
                // 首先检查扫描的编号是否与患者编号一致
                if (scannedId != _currentPatientInfo.PatientId)
                {
                    return QueueVerificationResult.PatientIdMismatch;
                }

                // 查询队列信息
                QueueInfo queueInfo = await _dataService.GetQueueInfoAsync(_currentPatientInfo.StudyId);

                if (queueInfo == null)
                {
                    // 队列中未找到患者
                    return QueueVerificationResult.QueueNotFound;
                }

                // 比较姓名和性别
                bool infoMatches = queueInfo.PatientName == _currentPatientInfo.PatientName &&
                                  queueInfo.Sex == _currentPatientInfo.Sex;

                return infoMatches ? QueueVerificationResult.Success : QueueVerificationResult.InfoMismatch;
            }
            catch
            {
                // 发生异常时，视为队列未找到
                return QueueVerificationResult.QueueNotFound;
            }
        }

        /// <summary>
        /// 显示核对成功结果
        /// </summary>
        private void ShowVerificationSuccess()
        {
            VerificationResultBorder.Background = new SolidColorBrush(Color.FromRgb(212, 237, 218));
            VerificationResultBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(195, 230, 203));
            VerificationResultBorder.BorderThickness = new Thickness(2);

            ResultIconText.Text = "✔";
            ResultIconText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));

            ResultMessageText.Text = "核对通过！患者信息匹配成功";
            ResultMessageText.Foreground = new SolidColorBrush(Color.FromRgb(21, 87, 36));
        }

        /// <summary>
        /// 显示核对失败结果
        /// </summary>
        /// <param name="message">错误消息</param>
        private void ShowVerificationError(string message)
        {
            VerificationResultBorder.Background = new SolidColorBrush(Color.FromRgb(248, 215, 218));
            VerificationResultBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(245, 198, 203));
            VerificationResultBorder.BorderThickness = new Thickness(2);

            ResultIconText.Text = "❌";
            ResultIconText.Foreground = new SolidColorBrush(Color.FromRgb(114, 28, 36));

            ResultMessageText.Text = message;
            ResultMessageText.Foreground = new SolidColorBrush(Color.FromRgb(114, 28, 36));
        }

        /// <summary>
        /// 清空输入
        /// </summary>
        private void ClearInput()
        {
            // 如果已经核对过，不允许清空
            if (_isVerified)
            {
                return;
            }

            ScanInputTextBox.Text = "";
            VerificationResultBorder.Visibility = Visibility.Collapsed;
            QueueWarningBorder.Visibility = Visibility.Collapsed;

            ScanInputTextBox.Focus();
        }

        /// <summary>
        /// 开始倒计时自动关闭
        /// </summary>
        private void StartCountdownTimer()
        {
            // 从配置文件读取倒计时秒数
            _countdownSeconds = PatientCheckConfigHelper.GetCloseSecondsConfig();

            // 如果配置为0秒，立即关闭
            if (_countdownSeconds == 0)
            {
                Application.Current.Shutdown(0);
                return;
            }

            _countdownTimer = new DispatcherTimer();
            _countdownTimer.Interval = TimeSpan.FromSeconds(1);
            _countdownTimer.Tick += CountdownTimer_Tick;
            _countdownTimer.Start();

            // 立即更新标题显示倒计时
            UpdateCountdownTitle();
        }

        /// <summary>
        /// 倒计时计时器事件
        /// </summary>
        private void CountdownTimer_Tick(object sender, EventArgs e)
        {
            _countdownSeconds--;

            if (_countdownSeconds <= 0)
            {
                _countdownTimer.Stop();
                // 倒计时结束，自动关闭程序，退出码为0
                Application.Current.Shutdown(0);
            }
            else
            {
                UpdateCountdownTitle();
            }
        }

        /// <summary>
        /// 更新倒计时标题
        /// </summary>
        private void UpdateCountdownTitle()
        {
            TitleTextBlock.Text = $"影像系统入室核对-{_countdownSeconds}秒后自动关闭";
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            try
            {
                _dataService?.Dispose();
                _countdownTimer?.Stop();
            }
            catch
            {
                // 忽略清理时的异常
            }

            base.OnClosed(e);
        }

        /// <summary>
        /// 窗口关闭事件处理
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // 保存窗口位置
            SaveWindowPosition();

            // 根据核对状态设置退出码
            if (_verificationSuccess)
            {
                // 核对成功，退出码为0
                Application.Current.Shutdown(0);
            }
            else if (_verificationFailed)
            {
                // 核对失败，退出码为1
                Application.Current.Shutdown(1);
            }
            else
            {
                // 没有核对（既没成功也没失败），退出码为2
                Application.Current.Shutdown(2);
            }

            base.OnClosing(e);
        }

        /// <summary>
        /// 处理检查开始功能
        /// </summary>
        private async Task HandleCheckStartAsync()
        {
            int checkStartMode = PatientCheckConfigHelper.GetCheckStartConfig();

            switch (checkStartMode)
            {
                case 0: // 关闭
                    // 不处理检查开始，直接根据配置决定是否自动关闭
                    if (PatientCheckConfigHelper.GetAutoCloseConfig())
                    {
                        StartCountdownTimer();
                    }
                    break;

                case 1: // 自动
                    // 自动执行检查开始（传递true表示自动模式）
                    await ExecuteCheckStartAsync(true);
                    break;

                case 2: // 手动
                    // 显示检查开始按钮，隐藏核对和清空按钮
                    ShowCheckStartButton();
                    break;
            }
        }

        /// <summary>
        /// 显示检查开始按钮
        /// </summary>
        private void ShowCheckStartButton()
        {
            // 隐藏核对和清空按钮的网格
            ((Grid)VerifyButton.Parent).Visibility = Visibility.Collapsed;

            // 显示检查开始按钮
            CheckStartButtonGrid.Visibility = Visibility.Visible;

            // 设置检查开始按钮支持回车键
            CheckStartButton.KeyDown += (s, e) => { if (e.Key == Key.Enter) CheckStartButton_Click(s, e); };

            // 设置焦点到检查开始按钮
            CheckStartButton.Focus();
        }

        /// <summary>
        /// 执行检查开始
        /// </summary>
        /// <param name="isAutoMode">是否为自动模式</param>
        private async Task ExecuteCheckStartAsync(bool isAutoMode = false)
        {
            if (_checkStartCompleted)
            {
                return; // 避免重复执行
            }

            try
            {
                bool updateSuccess = await _dataService.UpdateCheckStartAsync(_checkSerialNum, _currentUserId, isAutoMode);

                if (updateSuccess)
                {
                    _checkStartCompleted = true;

                    // 更新结果显示
                    ResultMessageText.Text = "检查开始时间已记录！";

                    // 隐藏检查开始按钮
                    CheckStartButtonGrid.Visibility = Visibility.Collapsed;

                    // 根据配置决定是否自动关闭
                    if (PatientCheckConfigHelper.GetAutoCloseConfig())
                    {
                        StartCountdownTimer();
                    }
                }
                else
                {
                    MessageBox.Show("记录检查开始时间失败，请联系管理员！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"记录检查开始时间时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
